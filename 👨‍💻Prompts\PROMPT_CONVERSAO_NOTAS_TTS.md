# PROMPT PARA CONVERSÃO DE NOTAS OBSIDIAN PARA TEXT-TO-SPEECH  
  
## INSTRUÇÃO PRINCIPAL  
Converta as notas Obsidian localizadas em `C:\Obsidian Vault\📝Resumos\História GO` para versões TXT e DOCX limpas, adequadas para programas text-to-speech.  
  
## ESPECIFICAÇÕES TÉCNICAS  
  
### Pasta de Destino  
- **Local**: `C:\Obsidian Vault\Ouvir - Resumos\História GO`  
- **Exemplo**: Se as notas originais estão em `📝Resumos\História GO\`, salvar em `Ouvir - Resumos\História GO\`  
  
### Arquivo TXT  
- **Nome**: Mesmos nome das notas originais com extensão `.txt`  
- **Conteúdo**: Texto completamente limpo  
- **Remoções obrigatórias**:  
- Todas as tags HTML: `<span class="...">`, `</span>`  
- Todas as classes CSS: `conceito-importante`, `definicao`, `exemplo`, `atencao`, `jurisprudencia`, `artigo-lei`, `highlight-yellow-bold`, etc.  
- Formatações Markdown: `**texto**`, `*texto*`, `***texto***`  
- Códigos de destaque do Obsidian  
- **Preservar**: Estrutura hierárquica (títulos, subtítulos), conteúdo textual, quebras de linha  
  
### Arquivo DOCX  
- **Nome**: Mesmos nomes das notas originais com extensão `.docx`  
- **Fonte**: SF Pro Display  
- **Tamanho**: 16 pontos  
- **Alinhamento**: Justificado  
- **Conteúdo**: Mesmo texto limpo do arquivo TXT  
  
## PROCESSO DE EXECUÇÃO  
  
### Passo 1: Análise das Notas Originais  
```  
Analise as notas em C:\Obsidian Vault\Ouvir - Resumos\História GO identificando:  
- Estrutura do conteúdo  
- Tipos de formatação presentes  
- Tamanho aproximado do texto  
```  
  
### Passo 2: Criação do TXT  
```  
1. Extrair todo o texto das notas originais  
2. Remover TODAS as formatações HTML, CSS e Markdown  
3. Manter apenas texto puro com estrutura hierárquica  
4. Salvar como .txt na pasta de destino  
```  
  
### Passo 3: Criação do DOCX  
```  
Usar Python com python-docx:  
  
from docx import Document  
from docx.shared import Pt  
from docx.enum.text import WD_ALIGN_PARAGRAPH  
import os  
  
os.chdir(r'C:\Obsidian Vault')  
  
doc = Document()  
style = doc.styles['Normal']  
font = style.font  
font.name = 'SF Pro Display'  
font.size = Pt(16)  
paragraph_format = style.paragraph_format  
paragraph_format.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY  
  
with open('[CAMINHO_TXT]', 'r', encoding='utf-8') as f:  
content = f.read()  
  
lines = content.split('\n')  
for line in lines:  
if line.strip():  
p = doc.add_paragraph(line)  
for run in p.runs:  
run.font.name = 'SF Pro Display'  
run.font.size = Pt(16)  
p.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY  
else:  
doc.add_paragraph()  
  
doc.save('[CAMINHO_DOCX]')  
```  
  
## EXEMPLO DE USO  
```  
Converta a nota "C:\Obsidian Vault\📝Resumos\História GO\Aula 02.md"  
para versões TXT e DOCX na pasta "C:\Obsidian Vault\Ouvir - Resumos\História GO\"  
```  
  
## VALIDAÇÃO FINAL  
Verificar se:  
- ✅ Arquivos TXT criados sem formatações  
- ✅ Arquivos DOCX criados com fonte SF Pro Display tamanho 16  
- ✅ Ambos os arquivos têm o mesmo conteúdo textual  
- ✅ Estrutura hierárquica preservada  
- ✅ Adequados para text-to-speech  
  
## NOTAS IMPORTANTES  
- Se a pasta de destino não existir, criá-la automaticamente  
- Se python-docx não estiver instalado, instalar com: `pip install python-docx`  
- Sempre usar encoding UTF-8 para caracteres especiais  
- Manter quebras de linha para preservar a estrutura do texto